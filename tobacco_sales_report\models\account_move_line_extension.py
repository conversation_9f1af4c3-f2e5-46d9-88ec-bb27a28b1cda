# -*- coding: utf-8 -*-
"""
Account Move Line Extension for Tobacco Sales Reporting

This module extends account.move.line to add a manual stick count field
for cigar products that carries over from sale order lines, ensuring
consistent tracking from sales to invoicing.

IMPORTANT: NO FIELD DEFINITIONS IN PYTHON CODE!
The x_cigar_stick_count field must be created via Odoo Studio only.
This prevents interference with accounting calculations.
"""

from odoo import models, api


class AccountMoveLine(models.Model):
    """
    Extends account.move.line to provide stick count functionality for cigars.

    This extension provides methods to work with the x_cigar_stick_count field
    that should be created via Odoo Studio or XML data files, not Python field definitions.
    """
    _inherit = 'account.move.line'

    @api.depends('product_id')
    def _compute_is_cigar_product(self):
        """
        Compute whether the selected product is a cigar product.

        This computed field is used in views to show/hide the cigar stick count field
        only when a cigar product is selected.
        """
        for line in self:
            if hasattr(line, 'x_is_cigar_product'):
                line.x_is_cigar_product = (
                    line.product_id and
                    hasattr(line.product_id, 'is_cigar_category') and
                    line.product_id.is_cigar_category()
                )

    def get_cigar_stick_count(self):
        """
        Get the cigar stick count for this line.

        This method safely retrieves the stick count value, handling cases
        where the field might not exist or be accessible.

        Returns:
            int: The stick count value, or 0 if not available
        """
        return getattr(self, 'x_cigar_stick_count', 0)

    def set_cigar_stick_count(self, count):
        """
        Set the cigar stick count for this line.

        This method safely sets the stick count value, handling cases
        where the field might not exist.

        Args:
            count (int): The stick count to set
        """
        if hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = count

    def transfer_stick_count_from_sale_line(self, sale_line):
        """
        Transfer stick count from a sale order line to this invoice line.

        This method should be called when creating invoice lines from sale orders
        to preserve the manually entered stick count.

        Args:
            sale_line: sale.order.line record
        """
        if hasattr(sale_line, 'x_cigar_stick_count') and hasattr(self, 'x_cigar_stick_count'):
            self.x_cigar_stick_count = sale_line.x_cigar_stick_count


