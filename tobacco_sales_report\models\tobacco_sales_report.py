# -*- coding: utf-8 -*-
"""
Tobacco Sales Report Model

This model handles the generation of Excel reports for tobacco and cigar sales
with comprehensive customer and product information.
"""

import base64
import io
from datetime import date
from odoo import models, fields, api, _
from odoo.exceptions import UserError

try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None


class TobaccoSalesReport(models.TransientModel):
    """
    Model for generating tobacco sales Excel reports.
    
    This model collects sales data for tobacco and cigar products within a specified
    date range and generates a comprehensive Excel report with customer details,
    product information, and sales amounts.
    """
    _name = 'tobacco.sales.report'
    _description = 'Tobacco Sales Excel Report'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda _: date.today().replace(day=1)
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today
    )
    partner_ids = fields.Many2many(
        'res.partner',
        string='Customers',
        help="Select specific customers for the report. Leave empty to include all customers."
    )
    product_type = fields.Selection([
        ('both', 'Both Tobacco and Cigars'),
        ('tobacco', 'Tobacco Only'),
        ('cigars', 'Cigars Only'),
    ], string='Product Type', default='both', required=True,
       help="Select which product types to include in the report")
    report_file = fields.Binary(
        string='Report File',
        readonly=True
    )
    report_filename = fields.Char(
        string='Report Filename',
        readonly=True
    )
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated')
    ], default='draft')

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        """Validate that from date is not after to date."""
        for record in self:
            if record.date_from > record.date_to:
                raise UserError(_('From Date cannot be after To Date.'))

    def generate_report(self):
        """
        Generate the tobacco sales Excel report.
        
        Returns:
            dict: Action to download the generated report
        """
        if not xlsxwriter:
            raise UserError(_('xlsxwriter library is required. Please install it using: pip install xlsxwriter'))
        
        # Collect sales data
        sales_data = self._collect_sales_data()
        
        # Generate Excel file
        excel_file = self._generate_excel_file(sales_data)

        # Generate filename based on product type
        product_type_name = {
            'both': 'tobacco_cigar_sales',
            'tobacco': 'tobacco_sales',
            'cigars': 'cigar_sales'
        }.get(self.product_type, 'tobacco_sales')

        filename = f'{product_type_name}_report_{self.date_from}_{self.date_to}.xlsx'
        self.write({
            'report_file': base64.b64encode(excel_file),
            'report_filename': filename,
            'state': 'generated'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content?model={self._name}&id={self.id}&field=report_file&download=true&filename={filename}',
            'target': 'self',
        }

    def _collect_sales_data(self):
        """
        Collect sales data for tobacco and cigar products within the date range.
        
        Returns:
            list: List of dictionaries containing sales data
        """
        # Get all invoices within the date range that contain tobacco/cigar products
        domain = [
            ('type', '=', 'out_invoice'),
            ('state', '=', 'posted'),
            ('invoice_date', '>=', self.date_from),
            ('invoice_date', '<=', self.date_to),
        ]

        # Add customer filter if specified
        if self.partner_ids:
            domain.append(('partner_id', 'in', self.partner_ids.ids))
        
        invoices = self.env['account.move'].search(domain)
        sales_data = []
        
        for invoice in invoices:
            # Filter lines based on product type selection
            if self.product_type == 'tobacco':
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and line.product_id.is_tobacco_category()
                )
            elif self.product_type == 'cigars':
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and line.product_id.is_cigar_category()
                )
            else:  # both
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and (
                        line.product_id.is_tobacco_category() or
                        line.product_id.is_cigar_category()
                    )
                )
            
            if tobacco_lines:
                # Calculate totals for this invoice - sales amount for tobacco/cigar products only
                tobacco_total = sum(
                    line.price_subtotal for line in tobacco_lines
                    if line.product_id.is_tobacco_category()
                )
                cigar_total = sum(
                    line.price_subtotal for line in tobacco_lines
                    if line.product_id.is_cigar_category()
                )

                # Calculate actual stick count for cigars using manual entry
                cigar_stick_count = 0
                cigar_quantity_sold = 0
                for line in tobacco_lines:
                    if line.product_id.is_cigar_category():
                        cigar_quantity_sold += line.quantity
                        # Use manual stick count if available, otherwise use quantity (since UOM is unit)
                        manual_stick_count = getattr(line, 'x_cigar_stick_count', 0)
                        if manual_stick_count > 0:
                            cigar_stick_count += manual_stick_count
                        else:
                            # Since UOM is unit, quantity already represents individual cigars
                            cigar_stick_count += line.quantity
                
                # Calculate total ounces (will be implemented later)
                total_ounces = 0  # Placeholder for pipe tobacco calculation
                
                # Get the company name and address - use parent company if available, otherwise use partner
                partner = invoice.partner_id
                company_partner = partner.parent_id if partner.parent_id else partner
                customer_name = company_partner.name

                # Get federal ID - prefer parent company's VAT, fallback to contact's VAT if parent is empty
                federal_id = ''
                if company_partner.vat and company_partner.vat.strip():
                    federal_id = company_partner.vat.strip()
                elif partner.vat and partner.vat.strip():
                    federal_id = partner.vat.strip()

                sales_data.append({
                    'customer_name': customer_name or '',
                    'federal_id': federal_id,  # Using VAT field for Federal ID from company or contact
                    'sale_date': invoice.invoice_date,
                    'invoice_date': invoice.invoice_date,
                    'invoice_number': invoice.name or '',
                    'customer_address': self._format_address(company_partner),
                    'tobacco_sales_total': tobacco_total,
                    'cigar_quantity_sold': cigar_quantity_sold,  # Quantity sold
                    'cigar_stick_count': cigar_stick_count,     # Individual cigars sold
                    'cigar_sales_total': cigar_total,
                    'total_ounces': total_ounces,
                    'invoice_id': invoice.id,
                })
        
        return sales_data

    def _format_address(self, partner):
        """
        Format partner address into a single string.
        
        Args:
            partner: res.partner record
            
        Returns:
            str: Formatted address
        """
        address_parts = []
        if partner.street:
            address_parts.append(partner.street)
        if partner.street2:
            address_parts.append(partner.street2)
        if partner.city:
            address_parts.append(partner.city)
        if partner.state_id:
            address_parts.append(partner.state_id.name)
        if partner.zip:
            address_parts.append(partner.zip)
        if partner.country_id:
            address_parts.append(partner.country_id.name)
            
        return ', '.join(address_parts)

    def _generate_excel_file(self, sales_data):
        """
        Generate Excel file with tobacco sales data.

        Args:
            sales_data (list): List of sales data dictionaries

        Returns:
            bytes: Excel file content
        """
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('Tobacco Sales Report')

        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })

        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })

        data_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'top',
            'text_wrap': True
        })

        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })

        date_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': 'mm/dd/yyyy'
        })

        # Generate headers based on product type
        base_headers = [
            'Customer Name',
            'Federal ID',
            'Sale Date',
            'Invoice Date',
            'Invoice Number',
            'Customer Address'
        ]

        if self.product_type == 'tobacco':
            product_headers = [
                'Total Tobacco Cost',
                'Total Ounces'
            ]
        elif self.product_type == 'cigars':
            product_headers = [
                'Cigar Quantity',
                'Premium Cigar Stick Count',
                'Premium Cigar Cost'
            ]
        else:  # both
            product_headers = [
                'Total Tobacco Cost',
                'Cigar Quantity',
                'Premium Cigar Stick Count',
                'Premium Cigar Cost',
                'Total Ounces'
            ]

        headers = base_headers + product_headers

        # Set column widths dynamically based on number of columns
        base_widths = [25, 15, 12, 12, 15, 40]  # Customer Name, Federal ID, Sale Date, Invoice Date, Invoice Number, Customer Address
        product_widths = [15] * len(product_headers)  # Dynamic width for product columns

        all_widths = base_widths + product_widths
        for i, width in enumerate(all_widths):
            col_letter = chr(ord('A') + i)
            worksheet.set_column(f'{col_letter}:{col_letter}', width)

        # Generate title based on product type
        product_type_title = {
            'both': 'Tobacco & Cigar Sales Report',
            'tobacco': 'Tobacco Sales Report',
            'cigars': 'Cigar Sales Report'
        }.get(self.product_type, 'Tobacco Sales Report')

        # Write title with dynamic column range
        last_col = chr(ord('A') + len(headers) - 1)
        worksheet.merge_range(f'A1:{last_col}1', f'{product_type_title} ({self.date_from} to {self.date_to})', title_format)

        for col, header in enumerate(headers):
            worksheet.write(2, col, header, header_format)

        # Write data
        row = 3
        totals = {
            'tobacco_sales_total': 0,
            'cigar_sales_total': 0,
            'cigar_quantity_sold': 0,
            'cigar_stick_count': 0,
            'total_ounces': 0
        }

        for data in sales_data:
            col = 0
            # Write base data
            worksheet.write(row, col, data['customer_name'], data_format); col += 1
            worksheet.write(row, col, data['federal_id'], data_format); col += 1
            worksheet.write(row, col, data['sale_date'], date_format); col += 1
            worksheet.write(row, col, data['invoice_date'], date_format); col += 1
            worksheet.write(row, col, data['invoice_number'], data_format); col += 1
            worksheet.write(row, col, data['customer_address'], data_format); col += 1

            # Write product-specific data based on type
            if self.product_type == 'tobacco':
                worksheet.write(row, col, data['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, data['total_ounces'], number_format); col += 1
            elif self.product_type == 'cigars':
                worksheet.write(row, col, data['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, data['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, data['cigar_sales_total'], number_format); col += 1
            else:  # both
                worksheet.write(row, col, data['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, data['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, data['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, data['cigar_sales_total'], number_format); col += 1
                worksheet.write(row, col, data['total_ounces'], number_format); col += 1

            # Add to totals
            for key in totals:
                totals[key] += data[key]

            row += 1

        # Write totals row
        if sales_data:
            row += 1
            col = 5
            worksheet.write(row, col, 'TOTALS:', header_format)
            col += 1

            # Write totals based on product type
            if self.product_type == 'tobacco':
                worksheet.write(row, col, totals['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, totals['total_ounces'], number_format); col += 1
            elif self.product_type == 'cigars':
                worksheet.write(row, col, totals['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_sales_total'], number_format); col += 1
            else:  # both
                worksheet.write(row, col, totals['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_sales_total'], number_format); col += 1
                worksheet.write(row, col, totals['total_ounces'], number_format); col += 1

        workbook.close()
        output.seek(0)
        return output.read()
