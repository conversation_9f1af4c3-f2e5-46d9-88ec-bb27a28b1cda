# Field Creation Instructions

## IMPORTANT: Create Fields via Odoo Studio Only!

To avoid unbalanced entry errors, the `x_cigar_stick_count` and `x_is_cigar_product` fields must be created via Odoo Studio, NOT through Python code or XML data files.

**NOTE: User has already created the x_is_cigar_product field via Studio.**

## Steps to Create the Fields

### 1. Create Field on Sale Order Lines

1. Go to **Settings > Technical > Database Structure > Models**
2. Search for and open **sale.order.line**
3. Click **Add a Field** (or use Studio)
4. Create field with these settings:
   - **Field Name**: `x_cigar_stick_count`
   - **Field Label**: `Cigar Stick Count`
   - **Field Type**: `Integer`
   - **Default Value**: `0`
   - **Help Text**: `Manual entry for the actual number of individual cigar sticks being sold.`

### 2. Create Field on Invoice Lines

1. Go to **Settings > Technical > Database Structure > Models**
2. Search for and open **account.move.line**
3. Click **Add a Field** (or use Studio)
4. Create field with these settings:
   - **Field Name**: `x_cigar_stick_count`
   - **Field Label**: `Cigar Stick Count`
   - **Field Type**: `Integer`
   - **Default Value**: `0`
   - **Help Text**: `Manual entry for the actual number of individual cigar sticks being invoiced.`

### 3. Add Fields to Views (Optional)

If you want the fields to appear in the user interface:

1. Go to **Settings > Technical > User Interface > Views**
2. Find the sale order line form view
3. Add the field to the form
4. Repeat for invoice line views

## Why This Approach?

- **Prevents Unbalanced Entries**: Creating fields via Studio doesn't interfere with accounting calculations
- **Safe**: No risk of affecting debit/credit balance validation
- **Flexible**: Fields can be easily modified or removed via Studio
- **Supported**: This is the recommended way to add custom fields in Odoo

## Testing After Field Creation

1. **Update the module**: Restart Odoo and update the `tobacco_sales_report` module
2. **Test sale orders**: Create sale orders with cigar products
3. **Test invoicing**: Create invoices from sale orders
4. **Verify no errors**: Ensure no unbalanced entry errors occur
5. **Test reports**: Generate tobacco sales reports to verify stick count data

## Module Code Changes Made

The Python code has been updated to:
- Remove all field definitions from Python files
- Use safe `getattr()` calls to access fields
- Provide utility methods for working with stick count
- Handle cases where fields might not exist

This ensures the module works whether the fields exist or not, and prevents any interference with Odoo's accounting validation.
